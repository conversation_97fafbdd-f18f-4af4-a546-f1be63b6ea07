from flask import Flask, request, jsonify
from flask_cors import CORS
import razorpay
import pymysql
from pymongo import MongoClient
from datetime import datetime, timedelta
import logging
from logging.handlers import RotatingFileHandler
import config

app = Flask(__name__)
CORS(app, supports_credentials=True)

# Configure logging
log_handler = RotatingFileHandler('payment_service.log', maxBytes=1000000, backupCount=5)
log_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
log_handler.setFormatter(formatter)
app.logger.addHandler(log_handler)
app.logger.setLevel(logging.DEBUG)

# MySQL configurations (retained but not used for payment storage)
pay_dcfg = {
    "mysql": {
        "host": config.host,
        "user": config.user,
        "passwd": config.passwd,
        "db": "ielts_payment",
        "port": 3306
    }
}
ielts_dcfg = {
    "mysql": {
        "host": config.host,
        "user": config.user,
        "passwd": config.passwd,
        "db": "ielts_database",
        "port": 3306
    }
}

# Razorpay client
razorpay_client = razorpay.Client(auth=("rzp_test_m7ArlesvsvWAfQ", "9U9cMgEIwCCQhmiJarKE1wvz"))

# MongoDB connection
mongo_client = MongoClient('**************************************************')
db = mongo_client['ielts_payment']
payment_collection = db['payment_details']

# MySQL connection function (retained as per request)
def mysqlconnect(dcfg):
    app.logger.info("Attempting to connect to MySQL database: %s", dcfg["mysql"]["db"])
    try:
        return pymysql.connect(
            host=dcfg["mysql"]["host"],
            user=dcfg["mysql"]["user"],
            passwd=dcfg["mysql"]["passwd"],
            db=dcfg["mysql"]["db"],
            port=dcfg["mysql"]["port"]
        )
    except pymysql.Error as e:
        app.logger.error("Error connecting to MySQL: %s", e)
        return None

@app.route('/create_order', methods=['POST'])
def create_order():
    app.logger.debug("Received request to create order")
    data = request.json
    order_amount = data.get('amount')
    currency = data.get('currency')
    user_id = data.get('user_id')  # Accept user_id from request
    
    if not user_id:
        app.logger.error("User ID is required")
        return jsonify({"error": "User ID is required"}), 400

    try:
        order = razorpay_client.order.create({
            "amount": order_amount,
            "currency": currency,
            "payment_capture": '1'
        })
        app.logger.info("Order created with ID: %s for user_id: %s", order['id'], user_id)
        return jsonify(order_id=order['id'])
    except Exception as e:
        app.logger.error("Error creating order: %s", e)
        return jsonify({"error": "Failed to create order"}), 500

@app.route('/payment_success', methods=['POST'])
def payment_success():
    app.logger.debug("Received payment success notification")
    try:
        if not request.is_json:
            app.logger.error("Request is not JSON")
            return jsonify({"error": "Request must be JSON"}), 400

        payment_data = request.json
        app.logger.debug(f"Payment data received: {payment_data}")

        required_keys = ['payment_id', 'order_id', 'signature', 'plan_id', 'user_id']
        for key in required_keys:
            if key not in payment_data:
                app.logger.error(f"Missing field: {key}")
                return jsonify({"error": f"Missing field: {key}"}), 400

        payment_id = payment_data['payment_id']
        order_id = payment_data['order_id']
        signature = payment_data['signature']
        plan_id = payment_data['plan_id']
        user_id = payment_data['user_id']  # Get user_id dynamically from request
        center_code = payment_data.get('center_code', None)

        # Verify payment signature
        try:
            razorpay_client.utility.verify_payment_signature({
                'razorpay_order_id': order_id,
                'razorpay_payment_id': payment_id,
                'razorpay_signature': signature
            })
        except razorpay.errors.SignatureVerificationError as e:
            app.logger.error(f"Signature verification failed: {e}")
            return jsonify({"error": "Invalid signature"}), 400

        # Calculate expiry date based on plan_id
        expiry_map = {
            'prod_MonthlyPlan': timedelta(days=30),
            'prod_AnnualPlan': timedelta(days=365),
        }
        expiry_date = datetime.now() + expiry_map.get(plan_id, timedelta())
        created_at = datetime.now()
        status = 'success'

        # Prepare payment details for MongoDB
        payment_details = {
            'payment_id': payment_id,
            'order_id': order_id,
            'signature': signature,
            'user_id': user_id,  # Use dynamic user_id
            'plan_id': plan_id,
            'expiry_date': expiry_date,
            'created_at': created_at,
            'status': status,
            'center_code': center_code
        }

        # Insert into MongoDB
        payment_collection.insert_one(payment_details)
        app.logger.info("Payment details stored in MongoDB for user ID: %s", user_id)

        return jsonify({"message": "Payment details stored successfully"})

    except Exception as e:
        app.logger.error("General error: %s", e)
        return jsonify({"error": f"General error: {e}"}), 500

if __name__ == '__main__':
    app.run(port=4002, host="0.0.0.0", debug=True)