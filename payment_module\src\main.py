from flask import Flask, request, jsonify
from flask_cors import CORS
import razorpay
import pymysql
from pymongo import MongoClient
from datetime import datetime, timedelta
import logging
from logging.handlers import RotatingFileHandler
import config

app = Flask(__name__)
CORS(app, supports_credentials=True)

# Configure logging
log_handler = RotatingFileHandler('payment_service.log', maxBytes=1000000, backupCount=5)
log_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
log_handler.setFormatter(formatter)
app.logger.addHandler(log_handler)
app.logger.setLevel(logging.DEBUG)

# MySQL configurations (retained but not used for payment storage)
pay_dcfg = {
    "mysql": {
        "host": config.host,
        "user": config.user,
        "passwd": config.passwd,
        "db": "ielts_payment",
        "port": 3306
    }
}
ielts_dcfg = {
    "mysql": {
        "host": config.host,
        "user": config.user,
        "passwd": config.passwd,
        "db": "ielts_database",
        "port": 3306
    }
}

# Razorpay client
razorpay_client = razorpay.Client(auth=("rzp_test_m7ArlesvsvWAfQ", "9U9cMgEIwCCQhmiJarKE1wvz"))

# MongoDB connection
mongo_client = MongoClient('*************************************************/')
db = mongo_client['ielts_payment']
payment_collection = db['payment_details']

# MySQL connection function (retained as per request)
def mysqlconnect(dcfg):
    app.logger.info("Attempting to connect to MySQL database: %s", dcfg["mysql"]["db"])
    try:
        return pymysql.connect(
            host=dcfg["mysql"]["host"],
            user=dcfg["mysql"]["user"],
            passwd=dcfg["mysql"]["passwd"],
            db=dcfg["mysql"]["db"],
            port=dcfg["mysql"]["port"]
        )
    except pymysql.Error as e:
        app.logger.error("Error connecting to MySQL: %s", e)
        return None

@app.route('/create_order', methods=['POST'])
def create_order():
    app.logger.debug("Received request to create order")
    data = request.json
    order_amount = data.get('amount')
    currency = data.get('currency')
    user_id = data.get('user_id')  # Accept user_id from request
    
    if not user_id:
        app.logger.error("User ID is required")
        return jsonify({"error": "User ID is required"}), 400

    try:
        order = razorpay_client.order.create({
            "amount": order_amount,
            "currency": currency,
            "payment_capture": '1'
        })
        app.logger.info("Order created with ID: %s for user_id: %s", order['id'], user_id)
        return jsonify(order_id=order['id'])
    except Exception as e:
        app.logger.error("Error creating order: %s", e)
        return jsonify({"error": "Failed to create order"}), 500

@app.route('/payment_success', methods=['POST'])
def payment_success():
    app.logger.debug("Received payment success notification")
    try:
        if not request.is_json:
            app.logger.error("Request is not JSON")
            return jsonify({"error": "Request must be JSON"}), 400

        payment_data = request.json
        app.logger.debug(f"Payment data received: {payment_data}")

        required_keys = ['payment_id', 'order_id', 'signature', 'plan_id', 'user_id']
        for key in required_keys:
            if key not in payment_data:
                app.logger.error(f"Missing field: {key}")
                return jsonify({"error": f"Missing field: {key}"}), 400

        payment_id = payment_data['payment_id']
        order_id = payment_data['order_id']
        signature = payment_data['signature']
        plan_id = payment_data['plan_id']
        user_id = payment_data['user_id']  # Get user_id dynamically from request
        center_code = payment_data.get('center_code', None)

        # Verify payment signature
        try:
            razorpay_client.utility.verify_payment_signature({
                'razorpay_order_id': order_id,
                'razorpay_payment_id': payment_id,
                'razorpay_signature': signature
            })
        except razorpay.errors.SignatureVerificationError as e:
            app.logger.error(f"Signature verification failed: {e}")
            return jsonify({"error": "Invalid signature"}), 400

        # Calculate expiry date based on plan_id
        expiry_map = {
            'prod_MonthlyPlan': timedelta(days=30),
            'prod_AnnualPlan': timedelta(days=365),
        }
        expiry_date = datetime.now() + expiry_map.get(plan_id, timedelta())
        created_at = datetime.now()
        status = 'success'

        # Prepare payment details for MongoDB
        payment_details = {
            'payment_id': payment_id,
            'order_id': order_id,
            'signature': signature,
            'user_id': user_id,  # Use dynamic user_id
            'plan_id': plan_id,
            'expiry_date': expiry_date,
            'created_at': created_at,
            'status': status,
            'center_code': center_code
        }

        # Insert into MongoDB
        payment_collection.insert_one(payment_details)
        app.logger.info("Payment details stored in MongoDB for user ID: %s", user_id)

        return jsonify({"message": "Payment details stored successfully"})

    except Exception as e:
        app.logger.error("General error: %s", e)
        return jsonify({"error": f"General error: {e}"}), 500

@app.route('/get_user_plan/<int:user_id>', methods=['GET'])
def get_user_plan(user_id):
    """
    Fetch the current active plan details for a specific user.
    Returns plan information including expiry date for trial reminders.
    """
    app.logger.debug(f"Received request to get plan details for user_id: {user_id}")

    try:
        # Find the most recent active plan for the user
        user_plan = payment_collection.find_one(
            {"user_id": user_id, "status": "active"},
            sort=[("created_at", -1)]  # Get the most recent plan
        )

        if not user_plan:
            app.logger.info(f"No active plan found for user_id: {user_id}")
            return jsonify({"error": "No active plan found for this user"}), 404

        # Calculate days remaining until expiry
        current_time = datetime.now()
        expiry_date = user_plan['expiry_date']

        # Handle timezone-aware datetime objects
        if hasattr(expiry_date, 'tzinfo') and expiry_date.tzinfo is not None:
            # If expiry_date is timezone-aware, make current_time timezone-aware too
            from datetime import timezone
            current_time = current_time.replace(tzinfo=timezone.utc)

        days_remaining = (expiry_date - current_time).days
        is_expired = days_remaining < 0

        # Prepare response data
        plan_details = {
            "user_id": user_plan['user_id'],
            "plan_id": user_plan['plan_id'],
            "status": user_plan['status'],
            "created_at": user_plan['created_at'].isoformat() if user_plan['created_at'] else None,
            "expiry_date": user_plan['expiry_date'].isoformat() if user_plan['expiry_date'] else None,
            "days_remaining": max(0, days_remaining),  # Don't show negative days
            "is_expired": is_expired,
            "is_trial": user_plan['plan_id'] == 'trial_plan',
            "payment_id": user_plan.get('payment_id'),
            "center_code": user_plan.get('center_code')
        }

        app.logger.info(f"Plan details retrieved for user_id: {user_id}, plan: {user_plan['plan_id']}, days_remaining: {days_remaining}")
        return jsonify(plan_details), 200

    except Exception as e:
        app.logger.error(f"Error retrieving plan details for user_id {user_id}: {e}")
        return jsonify({"error": f"Failed to retrieve plan details: {str(e)}"}), 500

@app.route('/get_user_plan_history/<int:user_id>', methods=['GET'])
def get_user_plan_history(user_id):
    """
    Fetch all plan history for a specific user.
    Returns all subscriptions (active, expired, etc.) for the user.
    """
    app.logger.debug(f"Received request to get plan history for user_id: {user_id}")

    try:
        # Find all plans for the user, sorted by creation date (newest first)
        user_plans = list(payment_collection.find(
            {"user_id": user_id},
            sort=[("created_at", -1)]
        ))

        if not user_plans:
            app.logger.info(f"No plans found for user_id: {user_id}")
            return jsonify({"plans": [], "total_plans": 0}), 200

        # Process each plan
        plans_list = []
        current_time = datetime.now()

        for plan in user_plans:
            expiry_date = plan['expiry_date']

            # Handle timezone-aware datetime objects
            if hasattr(expiry_date, 'tzinfo') and expiry_date.tzinfo is not None:
                from datetime import timezone
                current_time_tz = current_time.replace(tzinfo=timezone.utc)
            else:
                current_time_tz = current_time

            days_remaining = (expiry_date - current_time_tz).days
            is_expired = days_remaining < 0

            plan_info = {
                "plan_id": plan['plan_id'],
                "status": plan['status'],
                "created_at": plan['created_at'].isoformat() if plan['created_at'] else None,
                "expiry_date": plan['expiry_date'].isoformat() if plan['expiry_date'] else None,
                "days_remaining": max(0, days_remaining),
                "is_expired": is_expired,
                "is_trial": plan['plan_id'] == 'trial_plan',
                "payment_id": plan.get('payment_id'),
                "order_id": plan.get('order_id'),
                "center_code": plan.get('center_code')
            }
            plans_list.append(plan_info)

        response_data = {
            "user_id": user_id,
            "plans": plans_list,
            "total_plans": len(plans_list)
        }

        app.logger.info(f"Plan history retrieved for user_id: {user_id}, total plans: {len(plans_list)}")
        return jsonify(response_data), 200

    except Exception as e:
        app.logger.error(f"Error retrieving plan history for user_id {user_id}: {e}")
        return jsonify({"error": f"Failed to retrieve plan history: {str(e)}"}), 500

if __name__ == '__main__':
    app.run(port=4002, host="0.0.0.0", debug=True)